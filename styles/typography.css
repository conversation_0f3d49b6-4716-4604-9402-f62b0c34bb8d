/* styles/typography.css */

/* Typography system based on Roboto font family */
body {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--secondary-white);
}

/* Heading styles - all use Roboto Bold - Mobile First */
h1, .h1 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  font-size: 1.75rem; /* Will be overridden by responsive styles below */
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--primary-gold);
}

h2, .h2 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  font-size: 1.5rem; /* Will be overridden by responsive styles below */
  line-height: 1.3;
  margin-bottom: 0.875rem;
  color: var(--secondary-white);
}

h3, .h3 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  font-size: 1.25rem; /* Will be overridden by responsive styles below */
  line-height: 1.3;
  margin-bottom: 0.75rem;
  color: var(--secondary-white);
}

h4, .h4 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  font-size: 1.125rem; /* Will be overridden by responsive styles below */
  line-height: 1.4;
  margin-bottom: 0.625rem;
  color: var(--secondary-white);
}

h5, .h5 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  color: var(--secondary-white);
}

h6, .h6 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
  color: var(--secondary-white);
}

/* Body text */
p {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  color: var(--secondary-white);
}

/* Links */
a {
  color: var(--secondary-light-blue);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: var(--primary-gold);
  text-decoration: underline;
}

/* Code and pre elements */
code, pre {
  font-family: 'Courier New', Courier, monospace;
  background-color: var(--neutral-light-gray);
  color: var(--primary-dark-blue-gray);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

pre {
  padding: 1rem;
  overflow-x: auto;
  border-radius: 8px;
  margin: 1rem 0;
  background-color: rgba(30, 41, 59, 0.5);
  border: 1px solid var(--neutral-very-light-gray);
}

pre code {
  background: transparent;
  padding: 0;
  color: var(--secondary-white);
}

/* Font weight utilities */
.font-roboto-regular {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
}

.font-roboto-bold {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
}

/* Text color utilities */
.text-primary {
  color: var(--primary-gold);
}

.text-secondary {
  color: var(--secondary-light-blue);
}

.text-muted {
  color: var(--neutral-light-gray);
}

/* Special typography effects */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-gold), #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px rgba(255, 199, 0, 0.5);
}

/* Responsive typography - Mobile First Approach */
/* Base styles for mobile */
h1, .h1 {
  font-size: 1.75rem; /* 28px */
  line-height: 1.2;
}

h2, .h2 {
  font-size: 1.5rem; /* 24px */
  line-height: 1.3;
}

h3, .h3 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.3;
}

h4, .h4 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.4;
}

/* Tablet and up */
@media (min-width: 768px) {
  h1, .h1 {
    font-size: 2rem; /* 32px */
  }
  
  h2, .h2 {
    font-size: 1.75rem; /* 28px */
  }
  
  h3, .h3 {
    font-size: 1.5rem; /* 24px */
  }
  
  h4, .h4 {
    font-size: 1.25rem; /* 20px */
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  h1, .h1 {
    font-size: 2.5rem; /* 40px */
  }
  
  h2, .h2 {
    font-size: 2rem; /* 32px */
  }
  
  h3, .h3 {
    font-size: 1.75rem; /* 28px */
  }
  
  h4, .h4 {
    font-size: 1.5rem; /* 24px */
  }
}

/* Large desktop */
@media (min-width: 1280px) {
  h1, .h1 {
    font-size: 3rem; /* 48px */
  }
}

/* Text size utilities for mobile-first responsive design */
.text-xs-mobile { font-size: 0.75rem; } /* 12px */
.text-sm-mobile { font-size: 0.875rem; } /* 14px */
.text-base-mobile { font-size: 1rem; } /* 16px */
.text-lg-mobile { font-size: 1.125rem; } /* 18px */
.text-xl-mobile { font-size: 1.25rem; } /* 20px */

/* Touch-friendly text sizing */
.text-touch {
  font-size: 1rem;
  line-height: 1.5;
  min-height: 44px;
  padding: 0.5rem;
}

/* Better readability for body text on mobile */
@media (max-width: 640px) {
  body {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.25rem;
  }
}
