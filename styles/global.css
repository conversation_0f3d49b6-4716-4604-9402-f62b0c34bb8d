/* styles/global.css */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './theme.css'; /* Import theme variables */
@import './typography.css'; /* Import typography styles */

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  @apply bg-primary-dark-blue-gray text-secondary-white font-roboto antialiased;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-light-gray;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-gold rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-yellow-500;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid theme('colors.primary.gold');
  outline-offset: 2px;
}

/* Button focus styles */
button:focus,
a:focus {
  @apply ring-2 ring-primary-gold ring-offset-2 ring-offset-primary-dark-blue-gray;
}

/* Global component classes */
@layer components {
  .btn-primary {
    @apply bg-primary-gold hover:bg-yellow-500 text-primary-dark-blue-gray font-bold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 focus:ring-2 focus:ring-primary-gold focus:ring-offset-2 active:scale-95;
  }
  
  .btn-secondary {
    @apply bg-secondary-light-blue hover:bg-blue-500 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 focus:ring-2 focus:ring-secondary-light-blue focus:ring-offset-2 active:scale-95;
  }
  
  .btn-outline {
    @apply border-2 border-primary-gold text-primary-gold hover:bg-primary-gold hover:text-primary-dark-blue-gray font-bold py-3 px-6 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-primary-gold focus:ring-offset-2 active:scale-95;
  }
  
  /* Mobile-optimized buttons */
  .btn-mobile {
    @apply py-4 px-8 text-lg min-h-[44px] min-w-[44px] touch-manipulation;
  }
  
  .card {
    @apply bg-slate-800 rounded-xl shadow-card hover:shadow-card-hover transition-all duration-300 border border-neutral-very-light-gray/10 backdrop-blur-sm;
  }
  
  .card-glow {
    @apply card hover:shadow-glow;
  }
  
  .card-interactive {
    @apply card cursor-pointer hover:scale-[1.02] active:scale-[0.98] transition-transform duration-200;
  }
  
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
  
  /* Responsive grid system */
  .grid-12 {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-12 gap-4 sm:gap-6;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4;
  }
  
  .grid-auto-fit {
    @apply grid grid-cols-auto-fit gap-6;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-gold via-yellow-400 to-yellow-300 bg-clip-text text-transparent;
  }
  
  .input-field {
    @apply w-full px-4 py-3 bg-slate-700 border border-neutral-very-light-gray/20 rounded-lg text-white placeholder-gray-400 focus:border-primary-gold focus:ring-1 focus:ring-primary-gold transition-colors duration-200 min-h-[44px];
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-primary-gold;
  }
  
  /* Mobile-first navigation */
  .nav-mobile {
    @apply fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-md border-t border-neutral-very-light-gray/10 p-4 sm:relative sm:bg-transparent sm:border-t-0 sm:p-0;
  }
  
  /* Touch-friendly elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }
  
  /* Glassmorphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
  }
  
  .glass-dark {
    @apply bg-slate-900/80 backdrop-blur-md border border-slate-700/50 rounded-xl;
  }
}

/* Animation utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }
  
  .animate-bounce-gentle {
    animation: bounceGentle 2s ease-in-out infinite;
  }
  
  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }
  
  /* Safe area utilities for mobile devices with notches */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Hide scrollbar but maintain functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Mobile-first spacing utilities */
  .space-mobile {
    padding: 1rem;
  }
  
  @media (min-width: 640px) {
    .space-mobile {
      padding: 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .space-mobile {
      padding: 2rem;
    }
  }
  
  /* Aspect ratio utilities */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-golden {
    aspect-ratio: 1.618 / 1;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}
