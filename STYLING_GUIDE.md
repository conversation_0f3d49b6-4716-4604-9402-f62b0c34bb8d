# Styling Guide for RoyaltyMusic Platform

## Design Philosophy
The design follows modern, clean, and intuitive principles that reflect the transparency and innovation of blockchain technology. The platform uses a mobile-first approach with a professional yet creative color palette.

## Color Palette

### Primary Colors
- **Gold/Yellow** `#FFC700` - Represents value, rewards, and growth
  - Tailwind classes: `bg-primary-gold`, `text-primary-gold`, `border-primary-gold`
- **Dark Blue/Gray** `#1E293B` - Background color for contrast and readability
  - Tailwind classes: `bg-primary-dark-blue-gray`, `text-primary-dark-blue-gray`

### Secondary Colors
- **White** `#FFFFFF` - Text and highlights
  - Tailwind classes: `bg-secondary-white`, `text-secondary-white`
- **Light Blue** `#818CF8` - Accents for buttons and interactive elements
  - Tailwind classes: `bg-secondary-light-blue`, `text-secondary-light-blue`

### Neutral Colors
- **Light Gray** `#F3F4F6` - Subtle backgrounds and separators
  - Tailwind classes: `bg-neutral-light-gray`, `text-neutral-light-gray`
- **Very Light Gray** `#E2E8F0` - Shadows and hover effects
  - Tailwind classes: `bg-neutral-very-light-gray`, `text-neutral-very-light-gray`

## Typography

### Font Family
- **Primary**: Roboto (Google Fonts) - Clean and modern
- **Headings**: Roboto Bold (font-weight: 700)
- **Body Text**: Roboto Regular (font-weight: 400)
- **Code Blocks**: Monospace (Courier New)

### Responsive Typography Classes
```css
.text-xs-mobile     /* 12px - Extra small mobile text */
.text-sm-mobile     /* 14px - Small mobile text */
.text-base-mobile   /* 16px - Base mobile text */
.text-lg-mobile     /* 18px - Large mobile text */
.text-xl-mobile     /* 20px - Extra large mobile text */
.text-touch         /* Touch-friendly text with proper sizing */
```

### Special Typography Effects
```css
.text-gradient      /* Gold gradient text effect */
.text-glow          /* Glowing text effect */
```

## Layout System

### Grid System
The platform uses a 12-column responsive grid system:

```css
.grid-12            /* Standard 12-column responsive grid */
.grid-responsive    /* Auto-responsive grid (1-6 columns) */
.grid-auto-fit      /* Auto-fit grid with minimum column width */
.container-custom   /* Responsive container with proper padding */
```

### Responsive Breakpoints
- **xs**: 475px and up
- **sm**: 640px and up (tablet)
- **md**: 768px and up
- **lg**: 1024px and up (desktop)
- **xl**: 1280px and up
- **2xl**: 1536px and up

## Component Classes

### Buttons
```css
.btn-primary        /* Primary gold button */
.btn-secondary      /* Secondary blue button */
.btn-outline        /* Outlined button */
.btn-mobile         /* Mobile-optimized button (larger touch targets) */
```

### Cards
```css
.card               /* Basic card with shadow and border */
.card-glow          /* Card with glow effect on hover */
.card-interactive   /* Interactive card with scale animations */
```

### Forms
```css
.input-field        /* Styled input field with focus states */
```

### Navigation
```css
.nav-mobile         /* Mobile-first navigation component */
.touch-target       /* Ensures minimum 44px touch target */
```

### Visual Effects
```css
.loading-spinner    /* Loading animation */
.glass              /* Glassmorphism effect (light) */
.glass-dark         /* Glassmorphism effect (dark) */
```

## Animation Classes

### Built-in Animations
```css
.animate-fade-in        /* Fade in animation */
.animate-fade-in-up     /* Fade in with upward motion */
.animate-slide-up       /* Slide up animation */
.animate-slide-down     /* Slide down animation */
.animate-pulse-glow     /* Pulsing glow effect */
.animate-bounce-gentle  /* Gentle bounce animation */
.animate-spin-slow      /* Slow spinning animation */
```

## Mobile-First Design Principles

### Touch Targets
- Minimum 44px x 44px for interactive elements
- Use `.touch-target` class for proper sizing
- Consider thumb reach zones on mobile devices

### Typography
- Base font size: 16px (prevents zoom on iOS)
- Line height: 1.6 for better readability
- Responsive scaling from mobile to desktop

### Layout
- Start with single-column layouts
- Progressive enhancement for larger screens
- Use flexbox and grid for responsive layouts

## Accessibility Features

### Focus States
- Custom focus rings using brand colors
- Proper contrast ratios
- Keyboard navigation support

### Color Contrast
- All text meets WCAG AA standards
- High contrast mode considerations
- Color is not the only means of conveying information

## Usage Examples

### Basic Layout
```tsx
<div className="container-custom">
  <div className="grid-12">
    <div className="card">
      <h2 className="text-gradient">Featured Artist</h2>
      <p className="text-secondary-white">Artist description...</p>
      <button className="btn-primary">View Profile</button>
    </div>
  </div>
</div>
```

### Mobile-Optimized Component
```tsx
<nav className="nav-mobile">
  <button className="btn-mobile touch-target">
    Mobile Action
  </button>
</nav>
```

### Responsive Typography
```tsx
<h1 className="text-xl-mobile md:text-3xl lg:text-4xl text-gradient">
  Responsive Heading
</h1>
```

## Best Practices

1. **Mobile-First**: Always design for mobile first, then enhance for larger screens
2. **Touch-Friendly**: Use appropriate touch target sizes (minimum 44px)
3. **Performance**: Use CSS animations over JavaScript when possible
4. **Consistency**: Stick to the defined color palette and spacing system
5. **Accessibility**: Ensure proper contrast and keyboard navigation
6. **Loading States**: Always provide feedback for async operations

## File Structure
- `tailwind.config.js` - Tailwind configuration with custom colors and utilities
- `styles/global.css` - Global styles and component classes
- `styles/typography.css` - Typography system and responsive text styles
- `styles/theme.css` - CSS custom properties for consistent theming
