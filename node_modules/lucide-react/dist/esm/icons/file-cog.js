/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "m2.305 15.53.923-.382", key: "yfp9st" }],
  ["path", { d: "m3.228 12.852-.924-.383", key: "bckynb" }],
  [
    "path",
    {
      d: "M4.677 21.5a2 2 0 0 0 1.313.5H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2.5",
      key: "1yo3oz"
    }
  ],
  ["path", { d: "m4.852 11.228-.383-.923", key: "1j88i9" }],
  ["path", { d: "m4.852 16.772-.383.924", key: "sag1dv" }],
  ["path", { d: "m7.148 11.228.383-.923", key: "rj39hk" }],
  ["path", { d: "m7.53 17.696-.382-.924", key: "1uu5cs" }],
  ["path", { d: "m8.772 12.852.923-.383", key: "13811l" }],
  ["path", { d: "m8.772 15.148.923.383", key: "z1a5l0" }],
  ["circle", { cx: "6", cy: "14", r: "3", key: "a1xfv6" }]
];
const FileCog = createLucideIcon("file-cog", __iconNode);

export { __iconNode, FileCog as default };
//# sourceMappingURL=file-cog.js.map
