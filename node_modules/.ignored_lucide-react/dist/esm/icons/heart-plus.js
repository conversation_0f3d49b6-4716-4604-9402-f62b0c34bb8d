/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M13.5 19.5 12 21l-7-7c-1.5-1.45-3-3.2-3-5.5A5.5 5.5 0 0 1 7.5 3c1.76 0 3 .5 4.5 2 1.5-1.5 2.74-2 4.5-2a5.5 5.5 0 0 1 5.402 6.5",
      key: "vd0vy5"
    }
  ],
  ["path", { d: "M15 15h6", key: "1u4692" }],
  ["path", { d: "M18 12v6", key: "1houu1" }]
];
const HeartPlus = createLucideIcon("heart-plus", __iconNode);

export { __iconNode, HeartPlus as default };
//# sourceMappingURL=heart-plus.js.map
